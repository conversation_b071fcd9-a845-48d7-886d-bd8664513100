// 植物病害检测页面 - JavaScript文件

// 全局变量
currentModel = null;
let currentImage = null;
let detectionResults = [];

// 页面加载完成后执行
$(document).ready(function() {
    console.log('检测页面已加载');
    
    // 初始化检测页面
    initializeDetectPage();
    
    // 绑定检测页面事件
    bindDetectEvents();
    
    // 加载检测历史
    loadDetectionHistory();
});

// 初始化检测页面
function initializeDetectPage() {
    // 设置默认值
    $('#confidenceThreshold').val(0.5);
    $('#confidenceValue').text('0.5');
    
    // 检查是否有保存的模型路径
    const savedModelPath = StorageManager.get('modelPath', 'runs/train/exp/weights/best.pt');
    $('#modelPath').val(savedModelPath);
    
    // 检查是否有保存的置信度
    const savedConfidence = StorageManager.get('confidenceThreshold', 0.5);
    $('#confidenceThreshold').val(savedConfidence);
    $('#confidenceValue').text(savedConfidence);
    
    // 更新系统状态
    updateSystemStatus();
}

// 绑定检测页面事件
function bindDetectEvents() {
    // 置信度滑块变化事件
    $('#confidenceThreshold').on('input', function() {
        const value = $(this).val();
        $('#confidenceValue').text(value);
        StorageManager.set('confidenceThreshold', parseFloat(value));
    });
    
    // 加载模型按钮点击事件
    $('#loadModelBtn').on('click', function() {
        loadModel();
    });
    
    // 图像上传事件
    $('#imageInput').on('change', function(e) {
        handleImageUpload(e.target.files);
    });
    
    // 开始检测按钮点击事件
    $('#detectBtn').on('click', function() {
        startDetection();
    });
    
    // 清除结果按钮点击事件
    $('#clearBtn').on('click', function() {
        clearResults();
    });
    
    // 批量检测按钮点击事件
    $('#batchDetectBtn').on('click', function() {
        startBatchDetection();
    });
    
    // 模型路径输入框回车事件
    $('#modelPath').on('keypress', function(e) {
        if (e.which === 13) {
            loadModel();
        }
    });
    
    // 文件夹路径输入框回车事件
    $('#folderPath').on('keypress', function(e) {
        if (e.which === 13) {
            startBatchDetection();
        }
    });
}

// 加载模型
function loadModel() {
    const modelPath = $('#modelPath').val().trim();
    
    if (!modelPath) {
        showNotification('请输入模型路径', 'warning');
        return;
    }
    
    // 保存模型路径
    StorageManager.set('modelPath', modelPath);
    
    // 显示加载提示
    showLoading('正在加载模型...');
    
    // 发送加载模型请求
    $.ajax({
        url: '/api/load_model',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ model_path: modelPath }),
        success: function(response) {
            hideLoading();
            
            if (response.success) {
                currentModel = modelPath;
                showNotification('模型加载成功', 'success');
                updateModelStatus('已加载', 'success');
                updateSystemStatus();
                enableDetectionFeatures();
                
                // 显示模型信息
                if (response.model_info) {
                    showModelInfo(response.model_info);
                }
            } else {
                showNotification(response.message || '模型加载失败', 'error');
                updateModelStatus('加载失败', 'danger');
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            showNotification('模型加载失败: ' + error, 'error');
            updateModelStatus('加载失败', 'danger');
        }
    });
}

// 处理图像上传
function handleImageUpload(files) {
    if (!files || files.length === 0) {
        return;
    }
    
    const file = files[0];
    
    // 验证文件类型
    if (!validateFileType(file, ['image/'])) {
        showNotification('请选择有效的图像文件', 'error');
        return;
    }
    
    // 验证文件大小 (16MB)
    if (!validateFileSize(file, 16 * 1024 * 1024)) {
        showNotification('图像文件过大，请选择小于16MB的文件', 'error');
        return;
    }
    
    // 保存当前图像
    currentImage = file;
    
    // 显示原始图像
    displayOriginalImage(file);
    
    // 启用检测按钮
    if (currentModel) {
        $('#detectBtn').prop('disabled', false);
    }
    
    // 清除之前的结果
    clearResultImage();
    clearDetectionInfo();
}

// 显示原始图像
function displayOriginalImage(file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        const container = $('#originalImageContainer');
        container.html(`
            <img src="${e.target.result}" alt="原始图像" class="img-fluid">
        `);
    };
    
    reader.readAsDataURL(file);
}

// 开始检测
function startDetection() {
    if (!currentModel) {
        showNotification('请先加载模型', 'warning');
        return;
    }
    
    if (!currentImage) {
        showNotification('请先上传图像', 'warning');
        return;
    }
    
    // 显示加载提示
    showLoading('正在执行病害检测...');
    
    // 准备表单数据
    const formData = new FormData();
    formData.append('image', currentImage);
    formData.append('confidence', $('#confidenceThreshold').val());
    
    // 发送检测请求
    $.ajax({
        url: '/api/detect',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            hideLoading();
            
            if (response.success) {
                // 显示检测结果
                displayDetectionResult(response);
                
                // 保存检测历史
                saveDetectionHistory(response);
                
                // 显示下载选项
                showDownloadOptions(response);
                
                showNotification('检测完成', 'success');
            } else {
                showNotification(response.message || '检测失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            showNotification('检测失败: ' + error, 'error');
        }
    });
}

// 显示检测结果
function displayDetectionResult(response) {
    // 显示结果图像
    if (response.result_image) {
        const container = $('#resultImageContainer');
        container.html(`
            <img src="data:image/jpeg;base64,${response.result_image}" alt="检测结果" class="img-fluid">
        `);
    }
    
    // 显示检测信息
    if (response.detection_info) {
        const infoContainer = $('#detectionInfo');
        infoContainer.html(`
            <div class="detection-stats">
                <h6 class="fw-bold mb-3">检测统计</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="stat-item d-flex justify-content-between mb-2">
                            <span>检测数量:</span>
                            <strong class="text-primary">${response.num_detections || 0}</strong>
                        </div>
                        <div class="stat-item d-flex justify-content-between mb-2">
                            <span>置信度阈值:</span>
                            <strong class="text-info">${$('#confidenceThreshold').val()}</strong>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stat-item d-flex justify-content-between mb-2">
                            <span>处理时间:</span>
                            <strong class="text-success">${extractProcessingTime(response.detection_info)}</strong>
                        </div>
                        <div class="stat-item d-flex justify-content-between mb-2">
                            <span>FPS:</span>
                            <strong class="text-warning">${extractFPS(response.detection_info)}</strong>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <h6 class="fw-bold mb-2">详细信息</h6>
                    <pre class="bg-light p-3 rounded small">${response.detection_info}</pre>
                </div>
            </div>
        `);
    }
}

// 提取处理时间
function extractProcessingTime(info) {
    const match = info.match(/处理时间: ([\d.]+)ms/);
    return match ? match[1] + 'ms' : 'N/A';
}

// 提取FPS
function extractFPS(info) {
    const match = info.match(/FPS: ([\d.]+)/);
    return match ? match[1] : 'N/A';
}

// 开始批量检测
function startBatchDetection() {
    if (!currentModel) {
        showNotification('请先加载模型', 'warning');
        return;
    }
    
    const folderPath = $('#folderPath').val().trim();
    
    if (!folderPath) {
        showNotification('请输入图像文件夹路径', 'warning');
        return;
    }
    
    // 显示加载提示
    showLoading('正在执行批量检测...');
    
    // 发送批量检测请求
    $.ajax({
        url: '/api/batch_detect',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            folder_path: folderPath,
            confidence: $('#confidenceThreshold').val()
        }),
        success: function(response) {
            hideLoading();
            
            if (response.success) {
                showNotification(response.message, 'success');
                
                // 显示结果路径
                if (response.results_path) {
                    showNotification(`结果保存在: ${response.results_path}`, 'info');
                }
            } else {
                showNotification(response.message || '批量检测失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            showNotification('批量检测失败: ' + error, 'error');
        }
    });
}

// 清除结果
function clearResults() {
    // 清除图像
    clearOriginalImage();
    clearResultImage();
    
    // 清除检测信息
    clearDetectionInfo();
    
    // 重置文件输入
    $('#imageInput').val('');
    
    // 禁用检测按钮
    $('#detectBtn').prop('disabled', true);
    
    // 清除当前图像
    currentImage = null;
    
    showNotification('结果已清除', 'info');
}

// 清除原始图像
function clearOriginalImage() {
    $('#originalImageContainer').html(`
        <div class="placeholder-image">
            <i class="fas fa-image fa-3x text-muted"></i>
            <p class="text-muted mt-2">请上传图像</p>
        </div>
    `);
}

// 清除结果图像
function clearResultImage() {
    $('#resultImageContainer').html(`
        <div class="placeholder-image">
            <i class="fas fa-search fa-3x text-muted"></i>
            <p class="text-muted mt-2">等待检测</p>
        </div>
    `);
}

// 清除检测信息
function clearDetectionInfo() {
    $('#detectionInfo').html(`
        <div class="text-center text-muted">
            <i class="fas fa-info-circle fa-2x mb-2"></i>
            <p>请先加载模型并上传图像进行检测</p>
        </div>
    `);
}

// 更新模型状态
function updateModelStatus(status, type) {
    const statusContainer = $('#modelStatus');
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'danger' ? 'alert-danger' : 'alert-warning';
    
    statusContainer.html(`
        <div class="alert ${alertClass} alert-sm mb-0">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} me-1"></i>
            ${status}
        </div>
    `);
}

// 更新系统状态
function updateSystemStatus() {
    const systemStatus = currentModel ? '就绪' : '未就绪';
    const modelInfo = currentModel ? '已加载' : '未加载';
    const classInfo = currentModel ? 'unhealthy' : '-';
    
    $('#systemStatus').text(systemStatus);
    $('#modelInfo').text(modelInfo);
    $('#classInfo').text(classInfo);
    
    // 更新状态颜色
    if (currentModel) {
        $('#systemStatus').removeClass('text-danger').addClass('text-success');
        $('#modelInfo').removeClass('text-danger').addClass('text-success');
    } else {
        $('#systemStatus').removeClass('text-success').addClass('text-danger');
        $('#modelInfo').removeClass('text-success').addClass('text-danger');
    }
}

// 启用检测功能
function enableDetectionFeatures() {
    $('#detectBtn').prop('disabled', false);
    $('#batchDetectBtn').prop('disabled', false);
}

// 显示模型信息
function showModelInfo(info) {
    // 这里可以显示详细的模型信息
    console.log('模型信息:', info);
}

// 显示下载选项
function showDownloadOptions(response) {
    const downloadOptions = $('#downloadOptions');
    
    let optionsHtml = '<div class="row g-3">';
    
    if (response.result_filename) {
        optionsHtml += `
            <div class="col-12">
                <div class="d-grid">
                    <a href="/download/${response.result_filename}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>下载检测结果图像
                    </a>
                </div>
            </div>
        `;
    }
    
    if (response.detection_info) {
        optionsHtml += `
            <div class="col-12">
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary" onclick="copyDetectionInfo()">
                        <i class="fas fa-copy me-2"></i>复制检测信息
                    </button>
                </div>
            </div>
        `;
    }
    
    optionsHtml += '</div>';
    
    downloadOptions.html(optionsHtml);
    
    // 显示下载模态框
    $('#downloadModal').modal('show');
}

// 复制检测信息
function copyDetectionInfo() {
    const detectionInfo = $('#detectionInfo pre').text();
    copyToClipboard(detectionInfo);
}

// 保存检测历史
function saveDetectionHistory(result) {
    const historyItem = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        imageName: currentImage ? currentImage.name : 'Unknown',
        numDetections: result.num_detections || 0,
        confidence: $('#confidenceThreshold').val(),
        result: result
    };
    
    detectionResults.unshift(historyItem);
    
    // 限制历史记录数量
    if (detectionResults.length > 10) {
        detectionResults = detectionResults.slice(0, 10);
    }
    
    // 保存到本地存储
    StorageManager.set('detectionHistory', detectionResults);
    
    // 更新显示
    updateDetectionHistory();
}

// 加载检测历史
function loadDetectionHistory() {
    const savedHistory = StorageManager.get('detectionHistory', []);
    detectionResults = savedHistory;
    updateDetectionHistory();
}

// 更新检测历史显示
function updateDetectionHistory() {
    const historyContainer = $('#detectionHistory');
    
    if (detectionResults.length === 0) {
        historyContainer.html(`
            <div class="text-center text-muted">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <p>暂无检测记录</p>
            </div>
        `);
        return;
    }
    
    let historyHtml = '<div class="list-group list-group-flush">';
    
    detectionResults.forEach(item => {
        const time = new Date(item.timestamp).toLocaleString('zh-CN');
        
        historyHtml += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${item.imageName}</h6>
                    <small class="text-muted">${time}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary rounded-pill">${item.numDetections} 个检测</span>
                    <br>
                    <small class="text-muted">置信度: ${item.confidence}</small>
                </div>
            </div>
        `;
    });
    
    historyHtml += '</div>';
    
    historyContainer.html(historyHtml);
}

// 键盘快捷键
$(document).on('keydown', function(e) {
    // Ctrl + Enter: 开始检测
    if (e.ctrlKey && e.which === 13) {
        if (!$('#detectBtn').prop('disabled')) {
            startDetection();
        }
    }
    
    // Ctrl + L: 加载模型
    if (e.ctrlKey && e.which === 76) {
        loadModel();
    }
    
    // Ctrl + C: 清除结果
    if (e.ctrlKey && e.which === 67) {
        clearResults();
    }
});

// 拖拽上传支持
$(document).on('dragover', function(e) {
    e.preventDefault();
    e.stopPropagation();
    $('body').addClass('dragover');
});

$(document).on('dragleave', function(e) {
    e.preventDefault();
    e.stopPropagation();
    $('body').removeClass('dragover');
});

$(document).on('drop', function(e) {
    e.preventDefault();
    e.stopPropagation();
    $('body').removeClass('dragover');
    
    const files = e.originalEvent.dataTransfer.files;
    if (files && files.length > 0) {
        handleImageUpload(files);
    }
});

// 添加拖拽样式
$('<style>')
    .prop('type', 'text/css')
    .html(`
        body.dragover::after {
            content: '释放文件以上传';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 9999;
            pointer-events: none;
        }
    `)
    .appendTo('head'); 